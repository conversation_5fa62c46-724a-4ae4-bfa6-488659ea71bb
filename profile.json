{"version": 1, "name": "profile2", "keyboard": "product_name", "base": {"pollingRate": 3, "winlock": false, "macOS": false, "comboOptimization": true, "adaptiveCalibration": true}, "keys": [{"x": 11, "y": 11, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Esc", "display": true, "value": "0x00000029", "pos": 1, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 12, "width": 34, "height": 33, "light_pos": "9", "effect_pos": "11", "name": "Insert", "display": true, "value": "0x00000049", "pos": 98, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 54, "y": 12, "width": 34, "height": 33, "light_pos": "15", "effect_pos": "29", "name": "my_computer", "display": true, "value": "0x02000194", "pos": 15, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 96, "y": 12, "width": 34, "height": 33, "light_pos": "21", "effect_pos": "47", "name": "browser", "display": true, "value": "0x02000223", "pos": 16, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 140, "y": 12, "width": 34, "height": 33, "light_pos": "27", "effect_pos": "65", "name": "email", "display": true, "value": "0x0200018a", "pos": 17, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 183, "y": 12, "width": 34, "height": 33, "light_pos": "33", "effect_pos": "83", "name": "system_multimedia", "display": true, "value": "0x02000183", "pos": 18, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 225, "y": 12, "width": 34, "height": 33, "light_pos": "39", "effect_pos": "101", "name": "previous_song", "display": true, "value": "0x020000b6", "pos": 19, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 268, "y": 12, "width": 34, "height": 33, "light_pos": "45", "effect_pos": "119", "name": "next_song", "display": true, "value": "0x020000b5", "pos": 20, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 310, "y": 12, "width": 34, "height": 33, "light_pos": "51", "effect_pos": "137", "name": "play_pause", "display": true, "value": "0x020000cd", "pos": 21, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 353, "y": 12, "width": 34, "height": 33, "light_pos": "57", "effect_pos": "155", "name": "pause", "display": true, "value": "0x020000b7", "pos": 22, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 396, "y": 12, "width": 34, "height": 33, "light_pos": "63", "effect_pos": "173", "name": "volume_up", "display": true, "value": "0x020000e9", "pos": 23, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 439, "y": 12, "width": 34, "height": 33, "light_pos": "69", "effect_pos": "191", "name": "volume_down", "display": true, "value": "0x020000ea", "pos": 24, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 481, "y": 12, "width": 34, "height": 33, "light_pos": "75", "effect_pos": "209", "name": "mute", "display": true, "value": "0x020000e2", "pos": 25, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 526, "y": 12, "width": 34, "height": 33, "light_pos": "81", "effect_pos": "227", "name": "calculator", "display": true, "value": "0x02000192", "pos": 26, "layer": 2, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 566, "y": 12, "width": 77, "height": 33, "light_pos": "87", "effect_pos": "245", "name": "Backspace", "display": true, "value": "0x0000002a", "pos": 27, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 11, "y": 52, "width": 56, "height": 33, "light_pos": "10", "effect_pos": "14", "name": "Tab", "display": true, "value": "0x0000002b", "pos": 28, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 76, "y": 52, "width": 34, "height": 33, "light_pos": "16", "effect_pos": "32", "name": "Q", "display": true, "value": "0x00000014", "pos": 29, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 119, "y": 52, "width": 34, "height": 33, "light_pos": "22", "effect_pos": "50", "name": "W", "display": true, "value": "0x0000001a", "pos": 30, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 161, "y": 52, "width": 34, "height": 33, "light_pos": "28", "effect_pos": "68", "name": "E", "display": true, "value": "0x00000008", "pos": 31, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 204, "y": 52, "width": 34, "height": 33, "light_pos": "34", "effect_pos": "86", "name": "R", "display": true, "value": "0x00000015", "pos": 32, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 246, "y": 52, "width": 34, "height": 33, "light_pos": "40", "effect_pos": "104", "name": "T", "display": true, "value": "0x00000017", "pos": 33, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 289, "y": 52, "width": 34, "height": 33, "light_pos": "46", "effect_pos": "122", "name": "Y", "display": true, "value": "0x0000001c", "pos": 34, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 331, "y": 52, "width": 34, "height": 33, "light_pos": "52", "effect_pos": "140", "name": "U", "display": true, "value": "0x00000018", "pos": 35, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 374, "y": 52, "width": 34, "height": 33, "light_pos": "58", "effect_pos": "158", "name": "I", "display": true, "value": "0x0000000c", "pos": 36, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 417, "y": 52, "width": 34, "height": 33, "light_pos": "64", "effect_pos": "176", "name": "O", "display": true, "value": "0x00000012", "pos": 37, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 459, "y": 52, "width": 34, "height": 33, "light_pos": "70", "effect_pos": "194", "name": "P", "display": true, "value": "0x00000013", "pos": 38, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 503, "y": 52, "width": 34, "height": 33, "light_pos": "76", "effect_pos": "212", "name": "[{", "display": true, "value": "0x0000002f", "pos": 39, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 545, "y": 52, "width": 34, "height": 33, "light_pos": "82", "effect_pos": "230", "name": "]}", "display": true, "value": "0x00000030", "pos": 40, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 587, "y": 52, "width": 56, "height": 34, "light_pos": "88", "effect_pos": "248", "name": "\\ |", "display": true, "value": "0x00000031", "pos": 41, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 51, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Delete", "display": true, "value": "0x0000004c", "pos": 99, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 10, "y": 91, "width": 68, "height": 33, "light_pos": "11", "effect_pos": "17", "name": "Caps", "display": true, "value": "0x00000039", "pos": 42, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 85, "y": 91, "width": 34, "height": 33, "light_pos": "17", "effect_pos": "35", "name": "A", "display": true, "value": "0x00000004", "pos": 43, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 129, "y": 91, "width": 34, "height": 33, "light_pos": "23", "effect_pos": "53", "name": "S", "display": true, "value": "0x00000016", "pos": 44, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 171, "y": 91, "width": 34, "height": 33, "light_pos": "29", "effect_pos": "71 ", "name": "D", "display": true, "value": "0x00000007", "pos": 45, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 215, "y": 91, "width": 34, "height": 33, "light_pos": "35", "effect_pos": "89", "name": "F", "display": true, "value": "0x00000009", "pos": 46, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 256, "y": 91, "width": 34, "height": 33, "light_pos": "41", "effect_pos": "107", "name": "G", "display": true, "value": "0x0000000a", "pos": 47, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 300, "y": 91, "width": 34, "height": 33, "light_pos": "47", "effect_pos": "125", "name": "H", "display": true, "value": "0x0000000b", "pos": 48, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 341, "y": 91, "width": 34, "height": 33, "light_pos": "53", "effect_pos": "143", "name": "J", "display": true, "value": "0x0000000d", "pos": 49, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 384, "y": 91, "width": 34, "height": 33, "light_pos": "59", "effect_pos": "161", "name": "K", "display": true, "value": "0x0000000e", "pos": 50, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 428, "y": 91, "width": 34, "height": 33, "light_pos": "65", "effect_pos": "179", "name": "L", "display": true, "value": "0x0000000f", "pos": 51, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 471, "y": 91, "width": 34, "height": 33, "light_pos": "71", "effect_pos": "197", "name": ";:", "display": true, "value": "0x00000033", "pos": 52, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 513, "y": 91, "width": 34, "height": 33, "light_pos": "77", "effect_pos": "215", "name": "' \"", "display": true, "value": "0x00000034", "pos": 53, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 555, "y": 91, "width": 87, "height": 33, "light_pos": "89", "effect_pos": "251", "name": "Enter", "display": true, "value": "0x00000028", "pos": 54, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 91, "width": 34, "height": 33, "light_pos": "89", "effect_pos": "251", "name": "PgUp", "display": true, "value": "0x0000004b", "pos": 102, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 10, "y": 131, "width": 88, "height": 33, "light_pos": "12", "effect_pos": "20", "name": "L_Shift", "display": true, "value": "0x00020000", "pos": 55, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 108, "y": 131, "width": 34, "height": 33, "light_pos": "18", "effect_pos": "38", "name": "Z", "display": true, "value": "0x0000001d", "pos": 56, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 150, "y": 131, "width": 34, "height": 33, "light_pos": "24", "effect_pos": "56", "name": "X", "display": true, "value": "0x0000001b", "pos": 57, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 194, "y": 131, "width": 34, "height": 33, "light_pos": "30", "effect_pos": "74", "name": "C", "display": true, "value": "0x00000006", "pos": 58, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 235, "y": 131, "width": 34, "height": 33, "light_pos": "36", "effect_pos": "92", "name": "V", "display": true, "value": "0x00000019", "pos": 59, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 278, "y": 131, "width": 34, "height": 33, "light_pos": "42", "effect_pos": "110", "name": "B", "display": true, "value": "0x00000005", "pos": 60, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 320, "y": 131, "width": 34, "height": 33, "light_pos": "48", "effect_pos": "128", "name": "N", "display": true, "value": "0x00000011", "pos": 61, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 363, "y": 131, "width": 34, "height": 33, "light_pos": "54", "effect_pos": "146", "name": "M", "display": true, "value": "0x00000010", "pos": 62, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 407, "y": 131, "width": 34, "height": 33, "light_pos": "60", "effect_pos": "164", "name": ",<", "display": true, "value": "0x00000036", "pos": 63, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 449, "y": 131, "width": 34, "height": 33, "light_pos": "66", "effect_pos": "182", "name": ".>", "display": true, "value": "0x00000037", "pos": 64, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 492, "y": 131, "width": 34, "height": 33, "light_pos": "72", "effect_pos": "200", "name": "/?", "display": true, "value": "0x00000038", "pos": 65, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 531, "y": 131, "width": 69, "height": 32, "light_pos": "90", "effect_pos": "254", "name": "R_Shift", "display": true, "value": "0x00200000", "pos": 66, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 606, "y": 131, "width": 34, "height": 33, "light_pos": "90", "effect_pos": "254", "name": "Up", "display": true, "value": "0x00000052", "pos": 74, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 131, "width": 34, "height": 33, "light_pos": "90", "effect_pos": "254", "name": "PgDn", "display": true, "value": "0x0000004e", "pos": 103, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 11, "y": 171, "width": 45, "height": 33, "light_pos": "13", "effect_pos": "23", "name": "L_Ctrl", "display": true, "value": "0x00010000", "pos": 67, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 64, "y": 171, "width": 44, "height": 33, "light_pos": "19", "effect_pos": "41", "name": "L_WIN", "display": true, "value": "0x00080000", "pos": 68, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 119, "y": 171, "width": 42, "height": 33, "light_pos": "25", "effect_pos": "59", "name": "L_Alt", "display": true, "value": "0x00040000", "pos": 69, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 173, "y": 171, "width": 257, "height": 33, "light_pos": "43", "effect_pos": "113", "name": "Space", "display": true, "value": "0x0000002c", "pos": 70, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 436, "y": 171, "width": 34, "height": 33, "light_pos": "61", "effect_pos": "167", "name": "R_Alt", "display": true, "value": "0x00400000", "pos": 71, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 478, "y": 171, "width": 34, "height": 33, "light_pos": "67", "effect_pos": "185", "name": "Fn", "display": true, "value": "0x0d000000", "pos": 72, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 563, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Left", "display": true, "value": "0x00000050", "pos": 76, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 606, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Down", "display": true, "value": "0x00000051", "pos": 75, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Right", "display": true, "value": "0x0000004f", "pos": 77, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 522, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Fn1", "display": true, "value": "0x0d010000", "pos": 73, "layer": 2, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 11, "y": 11, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "`~", "display": true, "value": "0x00000035", "pos": 1, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 12, "width": 34, "height": 33, "light_pos": "9", "effect_pos": "11", "name": "Insert", "display": true, "value": "0x0700000b", "pos": 98, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 54, "y": 12, "width": 34, "height": 33, "light_pos": "15", "effect_pos": "29", "name": "F1", "display": true, "value": "0x0000003a", "pos": 15, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 96, "y": 12, "width": 34, "height": 33, "light_pos": "21", "effect_pos": "47", "name": "F2", "display": true, "value": "0x0000003b", "pos": 16, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 140, "y": 12, "width": 34, "height": 33, "light_pos": "27", "effect_pos": "65", "name": "F3", "display": true, "value": "0x0000003c", "pos": 17, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 183, "y": 12, "width": 34, "height": 33, "light_pos": "33", "effect_pos": "83", "name": "F4", "display": true, "value": "0x0000003d", "pos": 18, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 225, "y": 12, "width": 34, "height": 33, "light_pos": "39", "effect_pos": "101", "name": "F5", "display": true, "value": "0x0000003e", "pos": 19, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 268, "y": 12, "width": 34, "height": 33, "light_pos": "45", "effect_pos": "119", "name": "F6", "display": true, "value": "0x0000003f", "pos": 20, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 310, "y": 12, "width": 34, "height": 33, "light_pos": "51", "effect_pos": "137", "name": "F7", "display": true, "value": "0x00000040", "pos": 21, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 353, "y": 12, "width": 34, "height": 33, "light_pos": "57", "effect_pos": "155", "name": "F8", "display": true, "value": "0x00000041", "pos": 22, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 396, "y": 12, "width": 34, "height": 33, "light_pos": "63", "effect_pos": "173", "name": "F9", "display": true, "value": "0x00000042", "pos": 23, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 439, "y": 12, "width": 34, "height": 33, "light_pos": "69", "effect_pos": "191", "name": "F10", "display": true, "value": "0x00000043", "pos": 24, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 481, "y": 12, "width": 34, "height": 33, "light_pos": "75", "effect_pos": "209", "name": "F11", "display": true, "value": "0x00000044", "pos": 25, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 526, "y": 12, "width": 34, "height": 33, "light_pos": "81", "effect_pos": "227", "name": "F12", "display": true, "value": "0x00000045", "pos": 26, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 566, "y": 12, "width": 77, "height": 33, "light_pos": "87", "effect_pos": "245", "name": "Backspace", "display": true, "value": "0x0000002a", "pos": 27, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 11, "y": 52, "width": 56, "height": 33, "light_pos": "10", "effect_pos": "14", "name": "Tab", "display": true, "value": "0x0000002b", "pos": 28, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 76, "y": 52, "width": 34, "height": 33, "light_pos": "16", "effect_pos": "32", "name": "Q", "display": true, "value": "0x00000014", "pos": 29, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 119, "y": 52, "width": 34, "height": 33, "light_pos": "22", "effect_pos": "50", "name": "W", "display": true, "value": "0x0000001a", "pos": 30, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 161, "y": 52, "width": 34, "height": 33, "light_pos": "28", "effect_pos": "68", "name": "E", "display": true, "value": "0x00000008", "pos": 31, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 204, "y": 52, "width": 34, "height": 33, "light_pos": "34", "effect_pos": "86", "name": "R", "display": true, "value": "0x00000015", "pos": 32, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 246, "y": 52, "width": 34, "height": 33, "light_pos": "40", "effect_pos": "104", "name": "T", "display": true, "value": "0x00000017", "pos": 33, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 289, "y": 52, "width": 34, "height": 33, "light_pos": "46", "effect_pos": "122", "name": "Y", "display": true, "value": "0x0000001c", "pos": 34, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 331, "y": 52, "width": 34, "height": 33, "light_pos": "52", "effect_pos": "140", "name": "U", "display": true, "value": "0x00000018", "pos": 35, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 374, "y": 52, "width": 34, "height": 33, "light_pos": "58", "effect_pos": "158", "name": "I", "display": true, "value": "0x0000000c", "pos": 36, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 417, "y": 52, "width": 34, "height": 33, "light_pos": "64", "effect_pos": "176", "name": "O", "display": true, "value": "0x00000012", "pos": 37, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 459, "y": 52, "width": 34, "height": 33, "light_pos": "70", "effect_pos": "194", "name": "<PERSON><PERSON> Lock", "display": true, "value": "0x00000047", "pos": 38, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 503, "y": 52, "width": 34, "height": 33, "light_pos": "76", "effect_pos": "212", "name": "Home", "display": true, "value": "0x0000004a", "pos": 39, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 545, "y": 52, "width": 34, "height": 33, "light_pos": "82", "effect_pos": "230", "name": "End", "display": true, "value": "0x0000004d", "pos": 40, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 587, "y": 52, "width": 56, "height": 34, "light_pos": "88", "effect_pos": "248", "name": "\\ |", "display": true, "value": "0x00000031", "pos": 41, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 51, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Delete", "display": true, "value": "0x08020000", "pos": 99, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 10, "y": 91, "width": 68, "height": 33, "light_pos": "11", "effect_pos": "17", "name": "Caps", "display": true, "value": "0x00000039", "pos": 42, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 85, "y": 91, "width": 34, "height": 33, "light_pos": "17", "effect_pos": "35", "name": "Win", "display": true, "value": "0x0700000e", "pos": 43, "layer": 1, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 129, "y": 91, "width": 34, "height": 33, "light_pos": "23", "effect_pos": "53", "name": "<PERSON>", "display": true, "value": "0x0700000f", "pos": 44, "layer": 1, "type": "combination", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 171, "y": 91, "width": 34, "height": 33, "light_pos": "29", "effect_pos": "71 ", "name": "D", "display": true, "value": "0x00000007", "pos": 45, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 215, "y": 91, "width": 34, "height": 33, "light_pos": "35", "effect_pos": "89", "name": "F", "display": true, "value": "0x00000009", "pos": 46, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 256, "y": 91, "width": 34, "height": 33, "light_pos": "41", "effect_pos": "107", "name": "G", "display": true, "value": "0x0000000a", "pos": 47, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 300, "y": 91, "width": 34, "height": 33, "light_pos": "47", "effect_pos": "125", "name": "H", "display": true, "value": "0x0000000b", "pos": 48, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 341, "y": 91, "width": 34, "height": 33, "light_pos": "53", "effect_pos": "143", "name": "J", "display": true, "value": "0x0000000d", "pos": 49, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 384, "y": 91, "width": 34, "height": 33, "light_pos": "59", "effect_pos": "161", "name": "K", "display": true, "value": "0x0000000e", "pos": 50, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 428, "y": 91, "width": 34, "height": 33, "light_pos": "65", "effect_pos": "179", "name": "L", "display": true, "value": "0x0000000f", "pos": 51, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 471, "y": 91, "width": 34, "height": 33, "light_pos": "71", "effect_pos": "197", "name": "PrtSc", "display": true, "value": "0x00000046", "pos": 52, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 513, "y": 91, "width": 34, "height": 33, "light_pos": "77", "effect_pos": "215", "name": "Pause", "display": true, "value": "0x00000048", "pos": 53, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 555, "y": 91, "width": 87, "height": 33, "light_pos": "89", "effect_pos": "251", "name": "Enter", "display": true, "value": "0x00000028", "pos": 54, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 91, "width": 34, "height": 33, "light_pos": "89", "effect_pos": "251", "name": "PgUp", "display": true, "value": "0x08000000", "pos": 102, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 10, "y": 131, "width": 88, "height": 33, "light_pos": "12", "effect_pos": "20", "name": "L_Shift", "display": true, "value": "0x00020000", "pos": 55, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 108, "y": 131, "width": 34, "height": 33, "light_pos": "18", "effect_pos": "38", "name": "Z", "display": true, "value": "0x08040001", "pos": 56, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 150, "y": 131, "width": 34, "height": 33, "light_pos": "24", "effect_pos": "56", "name": "X", "display": true, "value": "0x08020001", "pos": 57, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 194, "y": 131, "width": 34, "height": 33, "light_pos": "30", "effect_pos": "74", "name": "C", "display": true, "value": "0x08030001", "pos": 58, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 235, "y": 131, "width": 34, "height": 33, "light_pos": "36", "effect_pos": "92", "name": "V", "display": true, "value": "0x09000000", "pos": 59, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 278, "y": 131, "width": 34, "height": 33, "light_pos": "42", "effect_pos": "110", "name": "B", "display": true, "value": "0x09000001", "pos": 60, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 320, "y": 131, "width": 34, "height": 33, "light_pos": "48", "effect_pos": "128", "name": "N", "display": true, "value": "0x09000002", "pos": 61, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 363, "y": 131, "width": 34, "height": 33, "light_pos": "54", "effect_pos": "146", "name": "M", "display": true, "value": "0x00000010", "pos": 62, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 407, "y": 131, "width": 34, "height": 33, "light_pos": "60", "effect_pos": "164", "name": ",<", "display": true, "value": "0x00000036", "pos": 63, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 449, "y": 131, "width": 34, "height": 33, "light_pos": "66", "effect_pos": "182", "name": ".>", "display": true, "value": "0x00000037", "pos": 64, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 492, "y": 131, "width": 34, "height": 33, "light_pos": "72", "effect_pos": "200", "name": "/?", "display": true, "value": "0x00000038", "pos": 65, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 531, "y": 131, "width": 69, "height": 32, "light_pos": "90", "effect_pos": "254", "name": "R_Shift", "display": true, "value": "0x00200000", "pos": 66, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 606, "y": 131, "width": 34, "height": 33, "light_pos": "90", "effect_pos": "254", "name": "Up", "display": true, "value": "0x08030100", "pos": 74, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 131, "width": 34, "height": 33, "light_pos": "90", "effect_pos": "254", "name": "PgDn", "display": true, "value": "0x0000004e", "pos": 103, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 11, "y": 171, "width": 45, "height": 33, "light_pos": "13", "effect_pos": "23", "name": "L_Ctrl", "display": true, "value": "0x00010000", "pos": 67, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 64, "y": 171, "width": 44, "height": 33, "light_pos": "19", "effect_pos": "41", "name": "L_WIN", "display": true, "value": "0x07000001", "pos": 68, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 119, "y": 171, "width": 42, "height": 33, "light_pos": "25", "effect_pos": "59", "name": "L_Alt", "display": true, "value": "0x00040000", "pos": 69, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 173, "y": 171, "width": 257, "height": 33, "light_pos": "43", "effect_pos": "113", "name": "Space", "display": true, "value": "0x07000004", "pos": 70, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 436, "y": 171, "width": 34, "height": 33, "light_pos": "61", "effect_pos": "167", "name": "R_Alt", "display": true, "value": "0x08000001", "pos": 71, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 478, "y": 171, "width": 34, "height": 33, "light_pos": "67", "effect_pos": "185", "name": "Fn", "display": true, "value": "0x0d000000", "pos": 72, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 563, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Left", "display": true, "value": "0x08040200", "pos": 76, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 606, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Down", "display": true, "value": "0x08030200", "pos": 75, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 650, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Right", "display": true, "value": "0x08040100", "pos": 77, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 522, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Fn1", "display": true, "value": "0x0d010000", "pos": 73, "layer": 1, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}}, {"x": 11, "y": 11, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Esc", "display": true, "value": "0x00000029", "pos": 1, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 650, "y": 12, "width": 34, "height": 33, "light_pos": "9", "effect_pos": "11", "name": "Insert", "display": true, "value": "0x00000049", "pos": 98, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 54, "y": 12, "width": 34, "height": 33, "light_pos": "15", "effect_pos": "29", "name": "1", "display": true, "value": "0x0000001e", "pos": 15, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 96, "y": 12, "width": 34, "height": 33, "light_pos": "21", "effect_pos": "47", "name": "2", "display": true, "value": "0x0000001f", "pos": 16, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 140, "y": 12, "width": 34, "height": 33, "light_pos": "27", "effect_pos": "65", "name": "3", "display": true, "value": "0x00000020", "pos": 17, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 183, "y": 12, "width": 34, "height": 33, "light_pos": "33", "effect_pos": "83", "name": "4", "display": true, "value": "0x00000021", "pos": 18, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 225, "y": 12, "width": 34, "height": 33, "light_pos": "39", "effect_pos": "101", "name": "5", "display": true, "value": "0x00000022", "pos": 19, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 268, "y": 12, "width": 34, "height": 33, "light_pos": "45", "effect_pos": "119", "name": "6", "display": true, "value": "0x00000023", "pos": 20, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 310, "y": 12, "width": 34, "height": 33, "light_pos": "51", "effect_pos": "137", "name": "7", "display": true, "value": "0x00000024", "pos": 21, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 353, "y": 12, "width": 34, "height": 33, "light_pos": "57", "effect_pos": "155", "name": "8", "display": true, "value": "0x00000025", "pos": 22, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 396, "y": 12, "width": 34, "height": 33, "light_pos": "63", "effect_pos": "173", "name": "9", "display": true, "value": "0x00000026", "pos": 23, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 439, "y": 12, "width": 34, "height": 33, "light_pos": "69", "effect_pos": "191", "name": "0", "display": true, "value": "0x00000027", "pos": 24, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 481, "y": 12, "width": 34, "height": 33, "light_pos": "75", "effect_pos": "209", "name": "-_", "display": true, "value": "0x0000002d", "pos": 25, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 526, "y": 12, "width": 34, "height": 33, "light_pos": "81", "effect_pos": "227", "name": "=+", "display": true, "value": "0x0000002e", "pos": 26, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 566, "y": 12, "width": 77, "height": 33, "light_pos": "87", "effect_pos": "245", "name": "Backspace", "display": true, "value": "0x0000002a", "pos": 27, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 11, "y": 52, "width": 56, "height": 33, "light_pos": "10", "effect_pos": "14", "name": "pro_dodge", "display": true, "value": {"name": "pro_dodge", "data_macro": [], "macro_type": {"type": "circle", "count": 1}, "macro_data": [{"name": "KeyA", "value": "DOWN", "duration": 85, "start_time": 0, "end_time": 85}, {"name": "KeyA", "value": "UP", "duration": 45, "start_time": 85, "end_time": 130}, {"name": "KeyD", "value": "DOWN", "duration": 85, "start_time": 130, "end_time": 215}, {"name": "KeyD", "value": "UP", "duration": 45, "start_time": 215, "end_time": 260}, {"name": "KeyA", "value": "DOWN", "duration": 75, "start_time": 260, "end_time": 335}, {"name": "KeyA", "value": "UP", "duration": 35, "start_time": 335, "end_time": 370}, {"name": "KeyD", "value": "DOWN", "duration": 95, "start_time": 370, "end_time": 465}, {"name": "KeyD", "value": "UP", "duration": 55, "start_time": 465, "end_time": 520}, {"name": "KeyA", "value": "DOWN", "duration": 65, "start_time": 520, "end_time": 585}, {"name": "KeyA", "value": "UP", "duration": 25, "start_time": 585, "end_time": 610}, {"name": "KeyD", "value": "DOWN", "duration": 70, "start_time": 610, "end_time": 680}, {"name": "KeyD", "value": "UP", "duration": 40, "start_time": 680, "end_time": 720}, {"name": "KeyA", "value": "DOWN", "duration": 80, "start_time": 720, "end_time": 800}, {"name": "KeyA", "value": "UP", "duration": 30, "start_time": 800, "end_time": 830}, {"name": "KeyD", "value": "DOWN", "duration": 90, "start_time": 830, "end_time": 920}, {"name": "KeyD", "value": "UP", "duration": 50, "start_time": 920, "end_time": 970}, {"name": "KeyA", "value": "DOWN", "duration": 60, "start_time": 970, "end_time": 1030}, {"name": "KeyA", "value": "UP", "duration": 20, "start_time": 1030, "end_time": 1050}, {"name": "KeyD", "value": "DOWN", "duration": 100, "start_time": 1050, "end_time": 1150}, {"name": "KeyD", "value": "UP", "duration": 60, "start_time": 1150, "end_time": 1210}, {"name": "KeyA", "value": "DOWN", "duration": 55, "start_time": 1210, "end_time": 1265}, {"name": "KeyA", "value": "UP", "duration": 15, "start_time": 1265, "end_time": 1280}, {"name": "KeyD", "value": "DOWN", "duration": 85, "start_time": 1280, "end_time": 1365}, {"name": "KeyD", "value": "UP", "duration": 45, "start_time": 1365, "end_time": 1410}], "type": "macro", "macro_idx": 0}, "pos": 28, "layer": 0, "type": "macro", "color": {"r": 0, "g": 255, "b": 128, "a": 255}, "advanced_key": {"anti_ghosting": true, "priority": "high", "input_method": "direct"}}, {"x": 76, "y": 52, "width": 34, "height": 33, "light_pos": "16", "effect_pos": "32", "name": "Q", "display": true, "value": "0x00000014", "pos": 29, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 119, "y": 52, "width": 34, "height": 33, "light_pos": "22", "effect_pos": "50", "name": "W", "display": true, "value": "0x0000001a", "pos": 30, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 161, "y": 52, "width": 34, "height": 33, "light_pos": "28", "effect_pos": "68", "name": "E", "display": true, "value": "0x00000008", "pos": 31, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 204, "y": 52, "width": 34, "height": 33, "light_pos": "34", "effect_pos": "86", "name": "R", "display": true, "value": "0x00000015", "pos": 32, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 246, "y": 52, "width": 34, "height": 33, "light_pos": "40", "effect_pos": "104", "name": "T", "display": true, "value": "0x00000017", "pos": 33, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 289, "y": 52, "width": 34, "height": 33, "light_pos": "46", "effect_pos": "122", "name": "Y", "display": true, "value": "0x0000001c", "pos": 34, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 331, "y": 52, "width": 34, "height": 33, "light_pos": "52", "effect_pos": "140", "name": "U", "display": true, "value": "0x00000018", "pos": 35, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 374, "y": 52, "width": 34, "height": 33, "light_pos": "58", "effect_pos": "158", "name": "I", "display": true, "value": "0x0000000c", "pos": 36, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 417, "y": 52, "width": 34, "height": 33, "light_pos": "64", "effect_pos": "176", "name": "O", "display": true, "value": "0x00000012", "pos": 37, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 459, "y": 52, "width": 34, "height": 33, "light_pos": "70", "effect_pos": "194", "name": "P", "display": true, "value": "0x00000013", "pos": 38, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 503, "y": 52, "width": 34, "height": 33, "light_pos": "76", "effect_pos": "212", "name": "[{", "display": true, "value": "0x0000002f", "pos": 39, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 545, "y": 52, "width": 34, "height": 33, "light_pos": "82", "effect_pos": "230", "name": "]}", "display": true, "value": "0x00000030", "pos": 40, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 587, "y": 52, "width": 56, "height": 34, "light_pos": "88", "effect_pos": "248", "name": "\\ |", "display": true, "value": "0x00000031", "pos": 41, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 650, "y": 51, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Delete", "display": true, "value": "0x0000004c", "pos": 99, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 10, "y": 91, "width": 68, "height": 33, "light_pos": "11", "effect_pos": "17", "name": "Caps", "display": true, "value": "0x00000039", "pos": 42, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 85, "y": 91, "width": 34, "height": 33, "light_pos": "17", "effect_pos": "35", "name": "A", "display": true, "value": "0x00000004", "pos": 43, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 129, "y": 91, "width": 34, "height": 33, "light_pos": "23", "effect_pos": "53", "name": "S", "display": true, "value": "0x00000016", "pos": 44, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 171, "y": 91, "width": 34, "height": 33, "light_pos": "29", "effect_pos": "71 ", "name": "D", "display": true, "value": "0x00000007", "pos": 45, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 215, "y": 91, "width": 34, "height": 33, "light_pos": "35", "effect_pos": "89", "name": "F", "display": true, "value": "0x00000009", "pos": 46, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 256, "y": 91, "width": 34, "height": 33, "light_pos": "41", "effect_pos": "107", "name": "G", "display": true, "value": "0x0000000a", "pos": 47, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 300, "y": 91, "width": 34, "height": 33, "light_pos": "47", "effect_pos": "125", "name": "H", "display": true, "value": "0x0000000b", "pos": 48, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 341, "y": 91, "width": 34, "height": 33, "light_pos": "53", "effect_pos": "143", "name": "J", "display": true, "value": "0x0000000d", "pos": 49, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 384, "y": 91, "width": 34, "height": 33, "light_pos": "59", "effect_pos": "161", "name": "K", "display": true, "value": "0x0000000e", "pos": 50, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 428, "y": 91, "width": 34, "height": 33, "light_pos": "65", "effect_pos": "179", "name": "L", "display": true, "value": "0x0000000f", "pos": 51, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 471, "y": 91, "width": 34, "height": 33, "light_pos": "71", "effect_pos": "197", "name": ";:", "display": true, "value": "0x00000033", "pos": 52, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 513, "y": 91, "width": 34, "height": 33, "light_pos": "77", "effect_pos": "215", "name": "' \"", "display": true, "value": "0x00000034", "pos": 53, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 555, "y": 91, "width": 87, "height": 33, "light_pos": "89", "effect_pos": "251", "name": "Enter", "display": true, "value": "0x00000028", "pos": 54, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 650, "y": 91, "width": 34, "height": 33, "light_pos": "89", "effect_pos": "251", "name": "PgUp", "display": true, "value": "0x0000004b", "pos": 102, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 10, "y": 131, "width": 88, "height": 33, "light_pos": "12", "effect_pos": "20", "name": "L_Shift", "display": true, "value": "0x00020000", "pos": 55, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 108, "y": 131, "width": 34, "height": 33, "light_pos": "18", "effect_pos": "38", "name": "Z", "display": true, "value": "0x0000001d", "pos": 56, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 150, "y": 131, "width": 34, "height": 33, "light_pos": "24", "effect_pos": "56", "name": "X", "display": true, "value": "0x0000001b", "pos": 57, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 194, "y": 131, "width": 34, "height": 33, "light_pos": "30", "effect_pos": "74", "name": "C", "display": true, "value": "0x00000006", "pos": 58, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 235, "y": 131, "width": 34, "height": 33, "light_pos": "36", "effect_pos": "92", "name": "V", "display": true, "value": "0x00000019", "pos": 59, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 278, "y": 131, "width": 34, "height": 33, "light_pos": "42", "effect_pos": "110", "name": "B", "display": true, "value": "0x00000005", "pos": 60, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 320, "y": 131, "width": 34, "height": 33, "light_pos": "48", "effect_pos": "128", "name": "N", "display": true, "value": "0x00000011", "pos": 61, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 363, "y": 131, "width": 34, "height": 33, "light_pos": "54", "effect_pos": "146", "name": "M", "display": true, "value": "0x00000010", "pos": 62, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 407, "y": 131, "width": 34, "height": 33, "light_pos": "60", "effect_pos": "164", "name": ",<", "display": true, "value": "0x00000036", "pos": 63, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 449, "y": 131, "width": 34, "height": 33, "light_pos": "66", "effect_pos": "182", "name": ".>", "display": true, "value": "0x00000037", "pos": 64, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 492, "y": 131, "width": 34, "height": 33, "light_pos": "72", "effect_pos": "200", "name": "/?", "display": true, "value": "0x00000038", "pos": 65, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 531, "y": 131, "width": 69, "height": 32, "light_pos": "90", "effect_pos": "254", "name": "R_Shift", "display": true, "value": "0x00200000", "pos": 66, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 606, "y": 131, "width": 34, "height": 33, "light_pos": "90", "effect_pos": "254", "name": "Up", "display": true, "value": "0x00000052", "pos": 74, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 650, "y": 131, "width": 34, "height": 33, "light_pos": "90", "effect_pos": "254", "name": "PgDn", "display": true, "value": "0x0000004e", "pos": 103, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 11, "y": 171, "width": 45, "height": 33, "light_pos": "13", "effect_pos": "23", "name": "L_Ctrl", "display": true, "value": "0x00010000", "pos": 67, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 64, "y": 171, "width": 44, "height": 33, "light_pos": "19", "effect_pos": "41", "name": "L_WIN", "display": true, "value": "0x00080000", "pos": 68, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 119, "y": 171, "width": 42, "height": 33, "light_pos": "25", "effect_pos": "59", "name": "L_Alt", "display": true, "value": "0x00040000", "pos": 69, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 173, "y": 171, "width": 257, "height": 33, "light_pos": "43", "effect_pos": "113", "name": "Space", "display": true, "value": "0x0000002c", "pos": 70, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 436, "y": 171, "width": 34, "height": 33, "light_pos": "61", "effect_pos": "167", "name": "R_Alt", "display": true, "value": "0x00400000", "pos": 71, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 478, "y": 171, "width": 34, "height": 33, "light_pos": "67", "effect_pos": "185", "name": "Fn", "display": true, "value": "0x0d000000", "pos": 72, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 563, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Left", "display": true, "value": "0x00000050", "pos": 76, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 606, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Down", "display": true, "value": "0x00000051", "pos": 75, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 650, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "Right", "display": true, "value": "0x0000004f", "pos": 77, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}, {"x": 522, "y": 171, "width": 34, "height": 33, "light_pos": "73", "effect_pos": "203", "name": "R_Ctrl", "display": true, "value": "0x00100000", "pos": 73, "layer": 0, "type": "basic", "color": {"r": 0, "g": 0, "b": 0, "a": 0}, "advanced_key": null}], "lighting": {"main": {"current": 0, "color": {"r": 255, "g": 255, "b": 255}, "brightness": 0, "speed": 0, "mixColor": true}, "side": {"current": 0, "color": {"r": 255, "g": 0, "b": 0}, "speed": 0, "mixColor": true}}, "performance": [{"key_type": 0, "distance": 1000, "rt_enable": false, "rt_up": 100, "rt_down": 100, "rt_split": false, "safe_area_enable": false, "safe_area_up": 100, "safe_area_down": 100}, {"key_type": 0, "distance": 1000, "rt_enable": false, "rt_up": 100, "rt_down": 100, "rt_split": false, "safe_area_enable": false, "safe_area_up": 100, "safe_area_down": 100}]}